import { <PERSON>Assister, TopScorer } from "@pro-clubs-manager/shared-dtos";
import { ClientSession, Types } from "mongoose";
import logger from "../config/logger";
import { NotFoundError, QueryFailedError } from "../errors";
import { ILeagueRepository } from "../interfaces/league/league-repository.interface";
import { IPlayerRepository } from "../interfaces/player/player-repository.interface";
import { ITeamRepository } from "../interfaces/team/team-repository.interface";
import League, { ILeague } from "../models/league";
import Game from "../models/game/game";
import { inject, injectable } from "tsyringe";
import { AllTimeTopAvgRatingByPosition, MostHattricks, MostCleanSheets, MostWinningPercentageTeam, MostWinningPercentagePlayer } from "./game-repository";

@injectable()
export class LeagueRepository implements ILeagueRepository {
  constructor(
    @inject("IPlayerRepository") private playerRepository: IPlayerRepository,
    @inject("ITeamRepository") private teamRepository: ITeamRepository
  ) {}
  async getAllLeagues(): Promise<ILeague[]> {
    try {
      const leagues = await League.find();
      return leagues;
    } catch (error: any) {
      logger.error(error.message);
      throw new QueryFailedError(`Failed to get all leagues`);
    }
  }

  async getLeagueById(id: string | Types.ObjectId, session?: ClientSession): Promise<ILeague> {
    try {
      const league = await League.findById(id, {}, { session });
      if (!league) {
        throw new NotFoundError(`League with id ${id} not found`);
      }
      return league;
    } catch (error: any) {
      if (error instanceof NotFoundError) {
        throw error; // Re-throw NotFoundError
      } else {
        logger.error(error.message);
        throw new QueryFailedError(`Failed to get league by id ${id}`);
      }
    }
  }

  async isLeagueNameExists(name: string): Promise<boolean> {
    try {
      const exists = await League.exists({ name });
      return !!exists;
    } catch (error: any) {
      logger.error(error.message);
      throw new QueryFailedError(`Failed to check if league name exists`);
    }
  }

  async createLeague(name: string, imgUrl?: string | undefined, session?: ClientSession): Promise<ILeague> {
    try {
      const league = (await League.create({ name, imgUrl }, { session }))[0];
      return league;
    } catch (error: any) {
      logger.error(error.message);
      throw new QueryFailedError(`Failed to create league with name ${name}`);
    }
  }

  async deleteLeague(id: string | Types.ObjectId, session?: ClientSession): Promise<void> {
    try {
      const league = await League.findByIdAndDelete(id, { session });
      if (!league) {
        throw new NotFoundError(`League with id ${id} not found`);
      }
    } catch (e: any) {
      if (e instanceof NotFoundError) {
        throw e;
      } else {
        logger.error(e.message);
        throw new QueryFailedError(`Failed to delete league with id ${id}`);
      }
    }
  }
  async removeTeamFromLeague(leagueId: Types.ObjectId, teamId: Types.ObjectId, session?: ClientSession | undefined): Promise<void> {
    try {
      const league = await League.updateOne({ _id: leagueId }, { $pull: { teams: teamId } }, { session });
      if (!league) {
        throw new NotFoundError(`League with id ${leagueId} not found`);
      }
    } catch (e: any) {
      if (e instanceof NotFoundError) {
        throw e;
      } else {
        logger.error(e.message);
        throw new QueryFailedError(`Failed to remove team from league with id ${leagueId}`);
      }
    }
  }

  async calculateLeagueTopScorers(leagueId: string, limit: number, session?: ClientSession): Promise<TopScorer[]> {
    try {
      // Get current season number for the league
      const currentSeasonNumber = await this.getCurrentSeasonNumber(leagueId);
      logger.info(`Calculating top scorers for league ${leagueId}, season ${currentSeasonNumber}`);

      // Use the same proven aggregation method as game repository
      const playerStats = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId),
            seasonNumber: currentSeasonNumber,
            result: { $exists: true } // Only completed games
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
            totalGames: { $sum: 1 }
          }
        },
        { $sort: { totalGoals: -1, totalGames: 1 } },
        { $limit: limit }
      ], { session });

      logger.info(`Found ${playerStats.length} players with stats for league ${leagueId}, season ${currentSeasonNumber}`);

      // Now get player and team information for each player
      const topScorers: TopScorer[] = [];

      for (const stats of playerStats) {
        try {
          // Get player information
          const player = await this.playerRepository.getPlayerById(stats._id, session);

          // Get team information
          let teamInfo = null;
          if (player.team) {
            teamInfo = await this.teamRepository.getTeamById(player.team, session);
          }

          const goalsPerGame = stats.totalGames > 0 ? stats.totalGoals / stats.totalGames : 0;

          topScorers.push({
            playerId: (player._id as Types.ObjectId).toString(),
            playerName: player.name,
            teamId: teamInfo ? (teamInfo._id as Types.ObjectId).toString() : '',
            teamName: teamInfo?.name || 'Free Agent',
            position: player.position,
            playerImgUrl: player.imgUrl,
            games: stats.totalGames,
            goals: stats.totalGoals,
            goalsPerGame: goalsPerGame
          });
        } catch (playerError: any) {
          logger.warn(`Could not get player info for ${stats._id}:`, playerError.message);
          // Skip this player if we can't get their info
          continue;
        }
      }

      logger.info(`Successfully calculated top scorers: ${topScorers.length} players`);
      return topScorers;
    } catch (e: any) {
      logger.error(`Error calculating top scorers for league ${leagueId}:`, e.message);
      throw new QueryFailedError(`Failed to calculate top scorers for league with id ${leagueId}`);
    }
  }

  async calculateLeagueTopAssisters(leagueId: string, limit: number, session?: ClientSession): Promise<TopAssister[]> {
    try {
      // Get current season number for the league
      const currentSeasonNumber = await this.getCurrentSeasonNumber(leagueId);
      logger.info(`Calculating top assisters for league ${leagueId}, season ${currentSeasonNumber}`);

      // Use the same proven aggregation method as game repository
      const playerStats = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId),
            seasonNumber: currentSeasonNumber,
            result: { $exists: true } // Only completed games
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
            totalGames: { $sum: 1 }
          }
        },
        { $sort: { totalAssists: -1, totalGames: 1 } },
        { $limit: limit }
      ], { session });

      logger.info(`Found ${playerStats.length} players with assist stats for league ${leagueId}, season ${currentSeasonNumber}`);

      // Now get player and team information for each player
      const topAssisters: TopAssister[] = [];

      for (const stats of playerStats) {
        try {
          // Get player information
          const player = await this.playerRepository.getPlayerById(stats._id, session);

          // Get team information
          let teamInfo = null;
          if (player.team) {
            teamInfo = await this.teamRepository.getTeamById(player.team, session);
          }

          const assistsPerGame = stats.totalGames > 0 ? stats.totalAssists / stats.totalGames : 0;

          topAssisters.push({
            playerId: (player._id as Types.ObjectId).toString(),
            playerName: player.name,
            teamId: teamInfo ? (teamInfo._id as Types.ObjectId).toString() : '',
            teamName: teamInfo?.name || 'Free Agent',
            position: player.position,
            playerImgUrl: player.imgUrl,
            games: stats.totalGames,
            assists: stats.totalAssists,
            assistsPerGame: assistsPerGame
          });
        } catch (playerError: any) {
          logger.warn(`Could not get player info for ${stats._id}:`, playerError.message);
          // Skip this player if we can't get their info
          continue;
        }
      }

      logger.info(`Successfully calculated top assisters: ${topAssisters.length} players`);
      return topAssisters;
    } catch (e: any) {
      logger.error(`Error calculating top assisters for league ${leagueId}:`, e.message);
      throw new QueryFailedError(`Failed to calculate top assisters for league with id ${leagueId}`);
    }
  }

  async calculateAllTimeTopScorers(leagueId: string, limit: number, session?: ClientSession): Promise<TopScorer[]> {
    try {
      // Use game-by-game aggregation method for accurate all-time stats
      const result = await Game.aggregate<TopScorer>(
        [
          { $match: { league: new Types.ObjectId(leagueId) } },
          {
            $project: {
              players: {
                $concatArrays: [
                  { $ifNull: ["$homeTeamPlayersPerformance", []] },
                  { $ifNull: ["$awayTeamPlayersPerformance", []] }
                ]
              }
            }
          },
          { $unwind: "$players" },
          {
            $group: {
              _id: "$players.playerId",
              totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
              totalGames: { $sum: 1 }
            }
          },
          {
            $addFields: {
              goalsPerGame: {
                $cond: {
                  if: { $eq: ["$totalGames", 0] },
                  then: 0,
                  else: { $divide: ["$totalGoals", "$totalGames"] }
                }
              }
            }
          },
          {
            $lookup: {
              from: "players",
              localField: "_id",
              foreignField: "_id",
              as: "player"
            }
          },
          { $unwind: "$player" },
          {
            $lookup: {
              from: "teams",
              localField: "player.team",
              foreignField: "_id",
              as: "team"
            }
          },
          { $unwind: "$team" },
          {
            $project: {
              playerId: "$_id",
              playerName: "$player.name",
              teamId: "$team._id",
              teamName: "$team.name",
              position: "$player.position",
              playerImgUrl: "$player.imgUrl",
              games: "$totalGames",
              goals: "$totalGoals",
              goalsPerGame: 1
            }
          },
          { $sort: { goals: -1 } },
          { $limit: limit }
        ],
        { session }
      );

      return result;
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to calculate all-time top scorers for league with id ${leagueId}`);
    }
  }

  async calculateAllTimeTopAssisters(leagueId: string, limit: number, session?: ClientSession): Promise<TopAssister[]> {
    try {
      return await Game.aggregate<TopAssister>(
        [
          { $match: { league: new Types.ObjectId(leagueId) } },
          {
            $project: {
              players: {
                $concatArrays: [
                  { $ifNull: ["$homeTeamPlayersPerformance", []] },
                  { $ifNull: ["$awayTeamPlayersPerformance", []] }
                ]
              }
            }
          },
          { $unwind: "$players" },
          {
            $group: {
              _id: "$players.playerId",
              totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
              totalGames: { $sum: 1 }
            }
          },
          {
            $addFields: {
              assistsPerGame: {
                $cond: {
                  if: { $eq: ["$totalGames", 0] },
                  then: 0,
                  else: { $divide: ["$totalAssists", "$totalGames"] }
                }
              }
            }
          },
          {
            $lookup: {
              from: "players",
              localField: "_id",
              foreignField: "_id",
              as: "player"
            }
          },
          { $unwind: "$player" },
          {
            $lookup: {
              from: "teams",
              localField: "player.team",
              foreignField: "_id",
              as: "team"
            }
          },
          { $unwind: "$team" },
          {
            $project: {
              playerId: "$_id",
              playerName: "$player.name",
              teamId: "$team._id",
              teamName: "$team.name",
              position: "$player.position",
              playerImgUrl: "$player.imgUrl",
              games: "$totalGames",
              assists: "$totalAssists",
              assistsPerGame: 1
            }
          },
          { $sort: { assists: -1 } },
          { $limit: limit }
        ],
        { session }
      );
    } catch (e: any) {
      logger.error(e.message);
      throw new QueryFailedError(`Failed to calculate all-time top assisters for league with id ${leagueId}`);
    }
  }

  /**
   * Get the current season number for a league
   * Uses the highest season number from games in the league
   */
  private async getCurrentSeasonNumber(leagueId: string): Promise<number> {
    try {
      logger.info(`Getting current season number for league: ${leagueId}`);

      const result = await Game.findOne({ league: new Types.ObjectId(leagueId) })
        .sort({ seasonNumber: -1 })
        .select("seasonNumber")
        .lean()
        .exec();

      const seasonNumber = result?.seasonNumber || 1;
      logger.info(`Current season number for league ${leagueId}: ${seasonNumber}`);
      return seasonNumber;
    } catch (error: any) {
      logger.error(`Error getting current season number for league ${leagueId}:`, error);
      // Return 1 as fallback to prevent server crashes
      return 1;
    }
  }

  /**
   * Sync stored player stats with actual game performance data
   * This method fixes discrepancies between stored stats and real game data
   */
  async syncPlayerStatsWithGameData(leagueId: string, session?: ClientSession): Promise<{ updated: number; errors: string[] }> {
    try {
      logger.info(`Starting player stats sync for league ${leagueId}`);

      const currentSeasonNumber = await this.getCurrentSeasonNumber(leagueId);

      // Get aggregated stats from games
      const gameStats = await Game.aggregate([
        {
          $match: {
            league: new Types.ObjectId(leagueId),
            seasonNumber: currentSeasonNumber,
            result: { $exists: true }
          }
        },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
            totalAssists: { $sum: { $ifNull: ["$players.assists", 0] } },
            totalGames: { $sum: 1 },
            totalPotm: { $sum: { $cond: [{ $eq: ["$players.playerOfTheMatch", true] }, 1, 0] } },
            totalCleanSheets: { $sum: { $cond: [{ $eq: ["$players.cleanSheet", true] }, 1, 0] } },
            avgRating: { $avg: "$players.rating" }
          }
        }
      ], { session });

      let updated = 0;
      const errors: string[] = [];

      // Update each player's stored stats
      for (const stats of gameStats) {
        try {
          const player = await this.playerRepository.getPlayerById(stats._id, session);

          if (player.currentSeason && player.currentSeason.seasonNumber === currentSeasonNumber) {
            // Update the stored stats with the accurate game data
            player.currentSeason.stats.goals = stats.totalGoals;
            player.currentSeason.stats.assists = stats.totalAssists;
            player.currentSeason.stats.games = stats.totalGames;
            player.currentSeason.stats.playerOfTheMatch = stats.totalPotm;
            player.currentSeason.stats.cleanSheets = stats.totalCleanSheets;
            player.currentSeason.stats.avgRating = stats.avgRating || 0;

            await player.save({ session });
            updated++;

            logger.info(`Updated stats for player ${player.name}: ${stats.totalGoals} goals, ${stats.totalAssists} assists, ${stats.totalGames} games`);
          }
        } catch (playerError: any) {
          const errorMsg = `Failed to update player ${stats._id}: ${playerError.message}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      logger.info(`Player stats sync completed: ${updated} players updated, ${errors.length} errors`);
      return { updated, errors };
    } catch (error: any) {
      logger.error(`Error syncing player stats for league ${leagueId}:`, error);
      throw new QueryFailedError(`Failed to sync player stats for league ${leagueId}`);
    }
  }

  async calculateAllTimeTopAvgRatingByPosition(leagueId: string, position: string, minimumGames: number, limit: number = 50, session?: ClientSession): Promise<AllTimeTopAvgRatingByPosition[]> {
    try {
      const pipeline: any[] = [
        { $match: { league: new Types.ObjectId(leagueId) } },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" }
      ];

      // Filter by position if not 'any'
      if (position.toLowerCase() !== 'any') {
        pipeline.push({ $match: { "players.positionPlayed": position } });
      }

      pipeline.push(
        {
          $group: {
            _id: {
              playerId: "$players.playerId",
              position: "$players.positionPlayed"
            },
            totalRating: { $sum: "$players.rating" },
            games: { $sum: 1 },
            goals: { $sum: { $ifNull: ["$players.goals", 0] } },
            assists: { $sum: { $ifNull: ["$players.assists", 0] } },
            cleanSheets: {
              $sum: { $cond: { if: "$players.cleanSheet", then: 1, else: 0 } }
            }
          }
        },
        {
          $project: {
            playerId: "$_id.playerId",
            position: "$_id.position",
            avgRating: { $divide: ["$totalRating", "$games"] },
            goals: 1,
            games: 1,
            assists: 1,
            cleanSheets: 1
          }
        },
        { $match: { games: { $gte: minimumGames } } },
        {
          $lookup: {
            from: "players",
            localField: "playerId",
            foreignField: "_id",
            as: "player"
          }
        },
        { $unwind: "$player" },
        {
          $lookup: {
            from: "teams",
            localField: "player.team",
            foreignField: "_id",
            as: "team"
          }
        },
        { $unwind: "$team" },
        {
          $project: {
            playerId: 1,
            playerName: "$player.name",
            playerImgUrl: "$player.imgUrl",
            teamId: "$team._id",
            teamName: "$team.name",
            teamImgUrl: "$team.imgUrl",
            goals: 1,
            position: 1,
            assists: 1,
            avgRating: 1,
            games: 1,
            cleanSheets: 1
          }
        },
        { $sort: { avgRating: -1 } },
        { $limit: limit }
      );

      const result = await Game.aggregate(pipeline, { session });
      return result;
    } catch (e: any) {
      logger.error(`Error calculating all-time top avg rating by position for league ${leagueId}:`, e);
      throw new QueryFailedError(`Failed to calculate all-time top avg rating by position for league ${leagueId}`);
    }
  }

  async calculateMostHattricks(leagueId: string, limit: number = 50, session?: ClientSession): Promise<MostHattricks[]> {
    try {
      const result = await Game.aggregate([
        { $match: { league: new Types.ObjectId(leagueId) } },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGoals: { $sum: { $ifNull: ["$players.goals", 0] } },
            totalGames: { $sum: 1 },
            hattricks: {
              $sum: {
                $cond: [
                  { $gte: [{ $ifNull: ["$players.goals", 0] }, 3] },
                  1,
                  0
                ]
              }
            }
          }
        },
        {
          $addFields: {
            hattricsPerGame: {
              $cond: {
                if: { $eq: ["$totalGames", 0] },
                then: 0,
                else: { $divide: ["$hattricks", "$totalGames"] }
              }
            }
          }
        },
        { $match: { hattricks: { $gt: 0 } } }, // Only players with at least one hattrick
        {
          $lookup: {
            from: "players",
            localField: "_id",
            foreignField: "_id",
            as: "player"
          }
        },
        { $unwind: "$player" },
        {
          $lookup: {
            from: "teams",
            localField: "player.team",
            foreignField: "_id",
            as: "team"
          }
        },
        { $unwind: "$team" },
        {
          $project: {
            playerId: "$_id",
            playerName: "$player.name",
            playerImgUrl: "$player.imgUrl",
            teamId: "$team._id",
            teamName: "$team.name",
            teamImgUrl: "$team.imgUrl",
            position: "$player.position",
            games: "$totalGames",
            totalGoals: 1,
            hattricks: 1,
            hattricsPerGame: 1
          }
        },
        { $sort: { hattricks: -1, hattricsPerGame: -1 } },
        { $limit: limit }
      ], { session });

      return result;
    } catch (e: any) {
      logger.error(`Error calculating most hattricks for league ${leagueId}:`, e);
      throw new QueryFailedError(`Failed to calculate most hattricks for league ${leagueId}`);
    }
  }

  async calculateMostCleanSheets(leagueId: string, limit: number = 50, session?: ClientSession): Promise<MostCleanSheets[]> {
    try {
      const result = await Game.aggregate([
        { $match: { league: new Types.ObjectId(leagueId) } },
        {
          $project: {
            players: {
              $concatArrays: [
                { $ifNull: ["$homeTeamPlayersPerformance", []] },
                { $ifNull: ["$awayTeamPlayersPerformance", []] }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $group: {
            _id: "$players.playerId",
            totalGames: { $sum: 1 },
            cleanSheets: {
              $sum: { $cond: [{ $eq: ["$players.cleanSheet", true] }, 1, 0] }
            },
            avgRating: { $avg: "$players.rating" }
          }
        },
        {
          $addFields: {
            cleanSheetsPerGame: {
              $cond: {
                if: { $eq: ["$totalGames", 0] },
                then: 0,
                else: { $divide: ["$cleanSheets", "$totalGames"] }
              }
            }
          }
        },
        { $match: { cleanSheets: { $gt: 0 } } }, // Only players with at least one clean sheet
        {
          $lookup: {
            from: "players",
            localField: "_id",
            foreignField: "_id",
            as: "player"
          }
        },
        { $unwind: "$player" },
        { $match: { "player.position": { $in: ["GK", "gk"] } } },
        {
          $lookup: {
            from: "teams",
            localField: "player.team",
            foreignField: "_id",
            as: "team"
          }
        },
        { $unwind: "$team" },
        {
          $project: {
            playerId: "$_id",
            playerName: "$player.name",
            playerImgUrl: "$player.imgUrl",
            teamId: "$team._id",
            teamName: "$team.name",
            teamImgUrl: "$team.imgUrl",
            position: "$player.position",
            games: "$totalGames",
            cleanSheets: 1,
            cleanSheetsPerGame: 1,
            avgRating: 1
          }
        },
        { $sort: { cleanSheets: -1, cleanSheetsPerGame: -1 } },
        { $limit: limit }
      ], { session });

      return result;
    } catch (e: any) {
      logger.error(`Error calculating most clean sheets for league ${leagueId}:`, e);
      throw new QueryFailedError(`Failed to calculate most clean sheets for league ${leagueId}`);
    }
  }

  async calculateMostWinningPercentageTeams(leagueId: string, minimumGames: number = 10, limit: number = 50, session?: ClientSession): Promise<MostWinningPercentageTeam[]> {
    try {
      // Debug: Check what games we're finding
      const totalGames = await Game.countDocuments({ league: new Types.ObjectId(leagueId) });
      const gamesWithResults = await Game.countDocuments({
        league: new Types.ObjectId(leagueId),
        result: { $exists: true, $ne: null }
      });
      const gamesWithStatus = await Game.countDocuments({
        league: new Types.ObjectId(leagueId),
        status: { $in: ["PLAYED", "COMPLETED", "Completed"] }
      });

      // Check what statuses exist
      const statusCounts = await Game.aggregate([
        { $match: { league: new Types.ObjectId(leagueId) } },
        { $group: { _id: "$status", count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]);

      logger.info(`Debug: League ${leagueId} - Total games: ${totalGames}, With results: ${gamesWithResults}, With PLAYED/COMPLETED status: ${gamesWithStatus}`);
      logger.info(`Debug: Status counts:`, JSON.stringify(statusCounts));

      // Get sample games with different criteria
      const sampleWithResults = await Game.findOne({
        league: new Types.ObjectId(leagueId),
        result: { $exists: true, $ne: null }
      }).select('result status homeTeam awayTeam');

      if (sampleWithResults) {
        logger.info(`Debug: Sample game with result - Status: ${sampleWithResults.status}, Result: ${JSON.stringify(sampleWithResults.result)}`);
      }

      const matchedGames = await Game.find({
        league: new Types.ObjectId(leagueId),
        result: { $exists: true, $ne: null },
        status: { $in: ["PLAYED", "COMPLETED", "Completed"] }
      }).limit(3).select('result status homeTeam awayTeam');

      logger.info(`Debug: Found ${matchedGames.length} games with results AND correct status for league ${leagueId}`);

      const result = await Game.aggregate([
        { $match: {
          league: new Types.ObjectId(leagueId),
          result: { $exists: true, $ne: null },
          status: { $in: ["PLAYED", "COMPLETED", "Completed"] }
        } },
        {
          $facet: {
            homeGames: [
              {
                $group: {
                  _id: "$homeTeam",
                  games: { $sum: 1 },
                  wins: {
                    $sum: {
                      $cond: [{ $gt: ["$result.homeTeamGoals", "$result.awayTeamGoals"] }, 1, 0]
                    }
                  },
                  draws: {
                    $sum: {
                      $cond: [{ $eq: ["$result.homeTeamGoals", "$result.awayTeamGoals"] }, 1, 0]
                    }
                  },
                  losses: {
                    $sum: {
                      $cond: [{ $lt: ["$result.homeTeamGoals", "$result.awayTeamGoals"] }, 1, 0]
                    }
                  },
                  goalsFor: { $sum: "$result.homeTeamGoals" },
                  goalsAgainst: { $sum: "$result.awayTeamGoals" }
                }
              }
            ],
            awayGames: [
              {
                $group: {
                  _id: "$awayTeam",
                  games: { $sum: 1 },
                  wins: {
                    $sum: {
                      $cond: [{ $gt: ["$result.awayTeamGoals", "$result.homeTeamGoals"] }, 1, 0]
                    }
                  },
                  draws: {
                    $sum: {
                      $cond: [{ $eq: ["$result.awayTeamGoals", "$result.homeTeamGoals"] }, 1, 0]
                    }
                  },
                  losses: {
                    $sum: {
                      $cond: [{ $lt: ["$result.awayTeamGoals", "$result.homeTeamGoals"] }, 1, 0]
                    }
                  },
                  goalsFor: { $sum: "$result.awayTeamGoals" },
                  goalsAgainst: { $sum: "$result.homeTeamGoals" }
                }
              }
            ]
          }
        },
        {
          $project: {
            allTeams: { $concatArrays: ["$homeGames", "$awayGames"] }
          }
        },
        { $unwind: "$allTeams" },
        {
          $group: {
            _id: "$allTeams._id",
            games: { $sum: "$allTeams.games" },
            wins: { $sum: "$allTeams.wins" },
            draws: { $sum: "$allTeams.draws" },
            losses: { $sum: "$allTeams.losses" },
            goalsFor: { $sum: "$allTeams.goalsFor" },
            goalsAgainst: { $sum: "$allTeams.goalsAgainst" }
          }
        },
        {
          $addFields: {
            winningPercentage: {
              $cond: {
                if: { $eq: ["$games", 0] },
                then: 0,
                else: { $multiply: [{ $divide: ["$wins", "$games"] }, 100] }
              }
            },
            goalDifference: { $subtract: ["$goalsFor", "$goalsAgainst"] }
          }
        },
        { $match: { games: { $gte: minimumGames } } },
        {
          $lookup: {
            from: "teams",
            localField: "_id",
            foreignField: "_id",
            as: "team"
          }
        },
        { $unwind: "$team" },
        {
          $project: {
            teamId: "$_id",
            teamName: "$team.name",
            teamImgUrl: "$team.imgUrl",
            games: 1,
            wins: 1,
            draws: 1,
            losses: 1,
            winningPercentage: 1,
            goalsFor: 1,
            goalsAgainst: 1,
            goalDifference: 1
          }
        },
        { $sort: { winningPercentage: -1, goalDifference: -1 } },
        { $limit: limit }
      ], { session });

      return result;
    } catch (e: any) {
      logger.error(`Error calculating most winning percentage teams for league ${leagueId}:`, e);
      throw new QueryFailedError(`Failed to calculate most winning percentage teams for league ${leagueId}`);
    }
  }

  async calculateMostWinningPercentagePlayers(leagueId: string, minimumGames: number = 10, limit: number = 50, session?: ClientSession): Promise<MostWinningPercentagePlayer[]> {
    try {
      const result = await Game.aggregate([
        { $match: {
          league: new Types.ObjectId(leagueId),
          result: { $exists: true, $ne: null },
          status: { $in: ["PLAYED", "COMPLETED", "Completed"] }
        } },
        {
          $project: {
            homeTeam: 1,
            awayTeam: 1,
            homeTeamGoals: "$result.homeTeamGoals",
            awayTeamGoals: "$result.awayTeamGoals",
            gameResult: {
              $cond: [
                { $gt: ["$result.homeTeamGoals", "$result.awayTeamGoals"] },
                "home_win",
                {
                  $cond: [
                    { $eq: ["$result.homeTeamGoals", "$result.awayTeamGoals"] },
                    "draw",
                    "away_win"
                  ]
                }
              ]
            },
            players: {
              $concatArrays: [
                {
                  $map: {
                    input: { $ifNull: ["$homeTeamPlayersPerformance", []] },
                    as: "player",
                    in: {
                      playerId: "$$player.playerId",
                      team: "$homeTeam",
                      isHome: true,
                      rating: "$$player.rating",
                      goals: { $ifNull: ["$$player.goals", 0] },
                      assists: { $ifNull: ["$$player.assists", 0] }
                    }
                  }
                },
                {
                  $map: {
                    input: { $ifNull: ["$awayTeamPlayersPerformance", []] },
                    as: "player",
                    in: {
                      playerId: "$$player.playerId",
                      team: "$awayTeam",
                      isHome: false,
                      rating: "$$player.rating",
                      goals: { $ifNull: ["$$player.goals", 0] },
                      assists: { $ifNull: ["$$player.assists", 0] }
                    }
                  }
                }
              ]
            }
          }
        },
        { $unwind: "$players" },
        {
          $addFields: {
            "players.gameResult": {
              $cond: [
                { $eq: ["$players.isHome", true] },
                {
                  $cond: [
                    { $eq: ["$gameResult", "home_win"] },
                    "win",
                    {
                      $cond: [
                        { $eq: ["$gameResult", "draw"] },
                        "draw",
                        "loss"
                      ]
                    }
                  ]
                },
                {
                  $cond: [
                    { $eq: ["$gameResult", "away_win"] },
                    "win",
                    {
                      $cond: [
                        { $eq: ["$gameResult", "draw"] },
                        "draw",
                        "loss"
                      ]
                    }
                  ]
                }
              ]
            }
          }
        },
        {
          $group: {
            _id: "$players.playerId",
            teamId: { $first: "$players.team" },
            games: { $sum: 1 },
            wins: {
              $sum: {
                $cond: [{ $eq: ["$players.gameResult", "win"] }, 1, 0]
              }
            },
            draws: {
              $sum: {
                $cond: [{ $eq: ["$players.gameResult", "draw"] }, 1, 0]
              }
            },
            losses: {
              $sum: {
                $cond: [{ $eq: ["$players.gameResult", "loss"] }, 1, 0]
              }
            },
            avgRating: { $avg: "$players.rating" },
            goals: { $sum: "$players.goals" },
            assists: { $sum: "$players.assists" }
          }
        },
        {
          $addFields: {
            winningPercentage: {
              $cond: {
                if: { $eq: ["$games", 0] },
                then: 0,
                else: { $multiply: [{ $divide: ["$wins", "$games"] }, 100] }
              }
            }
          }
        },
        { $match: { games: { $gte: minimumGames } } },
        {
          $lookup: {
            from: "players",
            localField: "_id",
            foreignField: "_id",
            as: "player"
          }
        },
        { $unwind: "$player" },
        {
          $lookup: {
            from: "teams",
            localField: "teamId",
            foreignField: "_id",
            as: "team"
          }
        },
        { $unwind: "$team" },
        {
          $project: {
            playerId: "$_id",
            playerName: "$player.name",
            playerImgUrl: "$player.imgUrl",
            teamId: "$teamId",
            teamName: "$team.name",
            teamImgUrl: "$team.imgUrl",
            position: "$player.position",
            games: 1,
            wins: 1,
            draws: 1,
            losses: 1,
            winningPercentage: 1,
            avgRating: 1,
            goals: 1,
            assists: 1
          }
        },
        { $sort: { winningPercentage: -1, avgRating: -1 } },
        { $limit: limit }
      ], { session });

      return result;
    } catch (e: any) {
      logger.error(`Error calculating most winning percentage players for league ${leagueId}:`, e);
      throw new QueryFailedError(`Failed to calculate most winning percentage players for league ${leagueId}`);
    }
  }
}
